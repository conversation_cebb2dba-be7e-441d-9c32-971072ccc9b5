import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Button from '@shape-construction/arch-ui/src/Button';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';
import { ScoringInfo } from './ScoringInfo';
import { useModal } from '@shape-construction/hooks';

export const HeatmapLegend = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  const { open, openModal, closeModal } = useModal(false);

  // State to manage which popover is open
  const [openPopovers, setOpenPopovers] = useState<Record<number, boolean>>({});

  const handleMouseEnter = (level: number) => {
    setOpenPopovers(prev => ({ ...prev, [level]: true }));
  };

  const handleMouseLeave = (level: number) => {
    setOpenPopovers(prev => ({ ...prev, [level]: false }));
  };

  const handleClick = (level: number) => {
    setOpenPopovers(prev => ({ ...prev, [level]: !prev[level] }));
  };

  return (
    <div className="overflow-x-auto">
      <div className="flex flex-row justify-between px-4 min-w-max items-center">
        <Popover open={open}>
          <Popover.Trigger>
            <Button
              color="secondary"
              size="xxs"
              variant="outlined"
              leadingIcon={InformationCircleIcon}
              onMouseOver={openModal}
              onMouseLeave={closeModal}
            >
              {messages('performanceDetails.issueReportsTable.scoringInfo.scoringInfoCTA')}
            </Button>
          </Popover.Trigger>
          <Popover.Content align="start" className="p-0 max-h-[340px]">
            <ScoringInfo />
          </Popover.Content>
        </Popover>

        <div className="flex">
          {HEATMAP_LEVELS.slice(1).map((level) => {
            const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
            return (
              <div key={level} className="p-2 last:pr-0">
                <Popover open={openPopovers[level] || false}>
                  <Popover.Trigger>
                    <div
                      onMouseEnter={() => handleMouseEnter(level)}
                      onMouseLeave={() => handleMouseLeave(level)}
                    >
                      <Badge label={messages(`healthLevels.${level}.label`)} className={badgeClasses} />
                    </div>
                  </Popover.Trigger>
                  <Popover.Content align="start" side='top' className='border-neutral-subtlest bg-surface-overlay p-3'>
                    Lorem ipsum dolor sit amet consectetur adipisicing elit. Cum quam, id atque quis commodi fugiat, mollitia possimus libero officia dolor impedit, animi quaerat dolore autem provident itaque. Nobis, non nulla!
                  </Popover.Content>
                </Popover>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};